@echo off
echo RemoteDesktop Test Runner
echo ========================

echo.
echo Building project...
cmake --build build --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Starting server in background...
start "RemoteDesktop Server" build\bin\Release\server.exe

echo.
echo Waiting 3 seconds for server to start...
timeout /t 3 /nobreak > nul

echo.
echo Starting client...
echo Note: The client will automatically connect to localhost
echo.
echo EXPECTED BEHAVIOR:
echo 1. Hidden desktop will be created using CreateDesktopA
echo 2. SwitchDesktop method will be tried first (desktop may flash briefly)
echo 3. If that fails, SetThreadDesktop method will be tried
echo 4. If both fail, main desktop capture will be used as fallback
echo 5. You should see successful screen capture in the server window
echo.

echo localhost | build\bin\Release\client.exe

echo.
echo Test completed. Check if the remote desktop connection worked.
pause
