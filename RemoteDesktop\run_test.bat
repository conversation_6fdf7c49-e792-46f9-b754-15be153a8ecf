@echo off
echo RemoteDesktop Test Runner
echo ========================

echo.
echo Building project...
cmake --build build --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Starting server in background...
start "RemoteDesktop Server" build\bin\Release\server.exe

echo.
echo Waiting 3 seconds for server to start...
timeout /t 3 /nobreak > nul

echo.
echo Starting client...
echo Note: The client will automatically connect to localhost
echo.
echo WORKING SOLUTION IMPLEMENTED:
echo =============================
echo Based on working code found online, we now use the CORRECT method
echo for capturing from hidden desktops created with CreateDesktopA!
echo.
echo NEW WORKING BEHAVIOR:
echo 1. Hidden desktop is created using CreateDesktopA
echo 2. Windows are enumerated using EnumDesktopWindows
echo 3. Each window is captured using PrintWindow API
echo 4. All window captures are composited into full desktop image
echo 5. Result: TRUE hidden desktop capture that actually works!
echo.
echo This uses the PrintWindow API designed for non-visible window capture!
echo.

echo localhost | build\bin\Release\client.exe

echo.
echo Test completed. Check if the remote desktop connection worked.
pause
