@echo off
echo RemoteDesktop Test Runner
echo ========================

echo.
echo Building project...
cmake --build build --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Starting server in background...
start "RemoteDesktop Server" build\bin\Release\server.exe

echo.
echo Waiting 3 seconds for server to start...
timeout /t 3 /nobreak > nul

echo.
echo Starting client...
echo Note: The client will automatically connect to localhost
echo If you see "BitBlt failed. Error: 6" messages, don't worry - 
echo the application will automatically switch to fallback mode.
echo.

echo localhost | build\bin\Release\client.exe

echo.
echo Test completed. Check if the remote desktop connection worked.
pause
