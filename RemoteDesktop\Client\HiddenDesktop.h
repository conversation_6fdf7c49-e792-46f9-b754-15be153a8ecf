#pragma once
#include <winsock2.h>
#include <windows.h>
#include <string>
#include <vector>

class HiddenDesktop {
private:
    HDESK hOriginalDesktop;
    HDESK hHiddenDesktop;
    HWINSTA hWindowStation;
    std::wstring desktopName;
    PROCESS_INFORMATION explorerProcess;
    bool isCreated;
    std::vector<HWND> testWindows; // Store test window handles

public:
    HiddenDesktop();
    ~HiddenDesktop();
    
    bool CreateHiddenDesktop();
    bool SwitchToHiddenDesktop();
    bool SwitchToOriginalDesktop();
    bool SwitchDesktopForCapture();  // Uses SwitchDesktop API
    bool SwitchBackFromCapture();    // Switches back using SwitchDesktop API
    bool StartExplorer();
    bool CreateTestWindow();         // Create a test window on hidden desktop
    void VerifyHiddenDesktopContent(); // Verify what's on the hidden desktop
    void Cleanup();
    HDESK GetDesktopHandle() const { return hHiddenDesktop; }
    const std::wstring& GetDesktopName() const { return desktopName; }
    HWND GetDesktopWindow() const;  // Get the desktop window handle for the hidden desktop
};