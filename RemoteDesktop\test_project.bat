@echo off
echo Testing RemoteDesktop Project
echo =============================

echo.
echo Building project...
cmake --build build --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Available executables:
dir build\bin\Release\*.exe

echo.
echo To test the project:
echo 1. Run 'build\bin\Release\server.exe' in one terminal
echo 2. Run 'build\bin\Release\client.exe' in another terminal
echo 3. Enter the server IP when prompted (or press Enter for localhost)
echo.
echo Note: This application requires administrator privileges for desktop capture
echo.
pause
