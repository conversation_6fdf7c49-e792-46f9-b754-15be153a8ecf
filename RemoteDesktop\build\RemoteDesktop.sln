﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{843206C4-7110-3DE3-B0C6-41E08B2DD736}"
	ProjectSection(ProjectDependencies) = postProject
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF} = {0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615} = {8C064BCD-77E4-34C2-A4BB-004BF35A0615}
		{435B1E27-6862-370A-8895-8E4A9B5C5F75} = {435B1E27-6862-370A-8895-8E4A9B5C5F75}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "client", "Client\client.vcxproj", "{8C064BCD-77E4-34C2-A4BB-004BF35A0615}"
	ProjectSection(ProjectDependencies) = postProject
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF} = {0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "server", "Server\server.vcxproj", "{435B1E27-6862-370A-8895-8E4A9B5C5F75}"
	ProjectSection(ProjectDependencies) = postProject
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF} = {0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{843206C4-7110-3DE3-B0C6-41E08B2DD736}.Debug|x64.ActiveCfg = Debug|x64
		{843206C4-7110-3DE3-B0C6-41E08B2DD736}.Release|x64.ActiveCfg = Release|x64
		{843206C4-7110-3DE3-B0C6-41E08B2DD736}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{843206C4-7110-3DE3-B0C6-41E08B2DD736}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}.Debug|x64.ActiveCfg = Debug|x64
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}.Debug|x64.Build.0 = Debug|x64
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}.Release|x64.ActiveCfg = Release|x64
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}.Release|x64.Build.0 = Release|x64
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{0EE0FAC9-7D50-39E6-ABDB-CEEC37AC37DF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615}.Debug|x64.ActiveCfg = Debug|x64
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615}.Debug|x64.Build.0 = Debug|x64
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615}.Release|x64.ActiveCfg = Release|x64
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615}.Release|x64.Build.0 = Release|x64
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8C064BCD-77E4-34C2-A4BB-004BF35A0615}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{435B1E27-6862-370A-8895-8E4A9B5C5F75}.Debug|x64.ActiveCfg = Debug|x64
		{435B1E27-6862-370A-8895-8E4A9B5C5F75}.Debug|x64.Build.0 = Debug|x64
		{435B1E27-6862-370A-8895-8E4A9B5C5F75}.Release|x64.ActiveCfg = Release|x64
		{435B1E27-6862-370A-8895-8E4A9B5C5F75}.Release|x64.Build.0 = Release|x64
		{435B1E27-6862-370A-8895-8E4A9B5C5F75}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{435B1E27-6862-370A-8895-8E4A9B5C5F75}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{435B1E27-6862-370A-8895-8E4A9B5C5F75}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{435B1E27-6862-370A-8895-8E4A9B5C5F75}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A389CF97-A51A-3EDE-944E-F8D293936AF5}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
