#include "ImageDisplay.h"
#include <iostream>
#include <functional>

ImageDisplay::ImageDisplay() 
    : hwnd(nullptr), hdc(nullptr), hBitmap(nullptr), 
      hOldBitmap(nullptr), hMemDC(nullptr), 
      imageWidth(0), imageHeight(0) {
}

ImageDisplay::~ImageDisplay() {
    if (hOldBitmap && hMemDC) {
        SelectObject(hMemDC, hOldBitmap);
    }
    if (hBitmap) DeleteObject(hBitmap);
    if (hMemDC) DeleteDC(hMemDC);
    if (hdc) ReleaseDC(hwnd, hdc);
    if (hwnd) DestroyWindow(hwnd);
}

bool ImageDisplay::CreateDisplayWindow() {
    WNDCLASSEXW wc = {};
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(nullptr);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = L"RemoteDesktopViewer";

    if (!RegisterClassExW(&wc)) {
        std::cerr << "Failed to register window class" << std::endl;
        return false;
    }

    hwnd = CreateWindowExW(
        0,
        L"RemoteDesktopViewer",
        L"Remote Desktop Viewer",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        800, 600,
        nullptr, nullptr,
        GetModuleHandle(nullptr),
        this
    );

    if (!hwnd) {
        std::cerr << "Failed to create window" << std::endl;
        return false;
    }

    hdc = GetDC(hwnd);
    hMemDC = CreateCompatibleDC(hdc);

    ShowWindow(hwnd, SW_SHOW);
    UpdateWindow(hwnd);

    return true;
}

void ImageDisplay::UpdateImage(const BYTE* data, int width, int height) {
    if (width != imageWidth || height != imageHeight) {
        // Recreate bitmap for new size
        if (hBitmap) {
            if (hOldBitmap) SelectObject(hMemDC, hOldBitmap);
            DeleteObject(hBitmap);
        }

        imageWidth = width;
        imageHeight = height;
        imageBuffer.resize(width * height * 4);

        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = -height; // Top-down
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 32;
        bmi.bmiHeader.biCompression = BI_RGB;

        hBitmap = CreateDIBSection(hdc, &bmi, DIB_RGB_COLORS, nullptr, nullptr, 0);
        if (hBitmap) {
            hOldBitmap = (HBITMAP)SelectObject(hMemDC, hBitmap);
        }
    }

    if (hBitmap) {
        // Copy image data
        memcpy(imageBuffer.data(), data, width * height * 4);

        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = -height;
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 32;
        bmi.bmiHeader.biCompression = BI_RGB;

        SetDIBits(hdc, hBitmap, 0, height, data, &bmi, DIB_RGB_COLORS);
        InvalidateRect(hwnd, nullptr, FALSE);
    }
}

void ImageDisplay::ProcessMessages() {
    MSG msg;
    while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
}

LRESULT CALLBACK ImageDisplay::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    ImageDisplay* display = nullptr;

    if (uMsg == WM_NCCREATE) {
        CREATESTRUCTW* cs = (CREATESTRUCTW*)lParam;
        display = (ImageDisplay*)cs->lpCreateParams;
        SetWindowLongPtr(hwnd, GWLP_USERDATA, (LONG_PTR)display);
    } else {
        display = (ImageDisplay*)GetWindowLongPtr(hwnd, GWLP_USERDATA);
    }

    if (display) {
        switch (uMsg) {
        case WM_PAINT:
            display->OnPaint();
            return 0;

        case WM_LBUTTONDOWN:
        case WM_LBUTTONUP:
        case WM_RBUTTONDOWN:
        case WM_RBUTTONUP:
        case WM_MOUSEMOVE:
        case WM_MOUSEWHEEL:
            display->OnMouseEvent(uMsg, wParam, lParam);
            return 0;

        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
        }
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

void ImageDisplay::OnPaint() {
    PAINTSTRUCT ps;
    HDC paintDC = BeginPaint(hwnd, &ps);

    if (hBitmap && hMemDC) {
        RECT clientRect;
        GetClientRect(hwnd, &clientRect);

        StretchBlt(
            paintDC,
            0, 0,
            clientRect.right, clientRect.bottom,
            hMemDC,
            0, 0,
            imageWidth, imageHeight,
            SRCCOPY
        );
    }

    EndPaint(hwnd, &ps);
}

void ImageDisplay::OnMouseEvent(UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (!OnMouseInput || imageWidth == 0 || imageHeight == 0) return;

    int x = LOWORD(lParam);
    int y = HIWORD(lParam);

    // Scale coordinates to remote desktop
    RECT clientRect;
    GetClientRect(hwnd, &clientRect);
    
    int scaledX = (x * imageWidth) / clientRect.right;
    int scaledY = (y * imageHeight) / clientRect.bottom;

    int flags = 0;
    int wheelDelta = 0;

    switch (uMsg) {
    case WM_LBUTTONDOWN:
        flags |= 0x01;
        break;
    case WM_LBUTTONUP:
        flags |= 0x02;
        break;
    case WM_RBUTTONDOWN:
        flags |= 0x04;
        break;
    case WM_RBUTTONUP:
        flags |= 0x08;
        break;
    case WM_MOUSEWHEEL:
        wheelDelta = GET_WHEEL_DELTA_WPARAM(wParam);
        break;
    }

    OnMouseInput(scaledX, scaledY, flags, wheelDelta);
}