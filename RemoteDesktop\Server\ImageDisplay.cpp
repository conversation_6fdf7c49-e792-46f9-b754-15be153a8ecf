#include "ImageDisplay.h"
#include <iostream>

// Context menu IDs
#define ID_MENU_CHROME      1001
#define ID_MENU_EDGE        1002
#define ID_MENU_FIREFOX     1003
#define ID_MENU_POWERSHELL  1004
#define ID_MENU_RUN         1005
#define ID_MENU_EXPLORER    1006
#define ID_MENU_NOTEPAD     1007
#include <functional>

ImageDisplay::ImageDisplay() 
    : hwnd(nullptr), hdc(nullptr), hBitmap(nullptr), 
      hOldBitmap(nullptr), hMemDC(nullptr), 
      imageWidth(0), imageHeight(0) {
}

ImageDisplay::~ImageDisplay() {
    if (hOldBitmap && hMemDC) {
        SelectObject(hMemDC, hOldBitmap);
    }
    if (hBitmap) DeleteObject(hBitmap);
    if (hMemDC) DeleteDC(hMemDC);
    if (hdc) ReleaseDC(hwnd, hdc);
    if (hwnd) DestroyWindow(hwnd);
}

bool ImageDisplay::CreateDisplayWindow() {
    WNDCLASSEXW wc = {};
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(nullptr);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = L"RemoteDesktopViewer";

    if (!RegisterClassExW(&wc)) {
        std::cerr << "Failed to register window class" << std::endl;
        return false;
    }

    hwnd = CreateWindowExW(
        0,
        L"RemoteDesktopViewer",
        L"Remote Desktop Viewer",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        800, 600,
        nullptr, nullptr,
        GetModuleHandle(nullptr),
        this
    );

    if (!hwnd) {
        std::cerr << "Failed to create window" << std::endl;
        return false;
    }

    hdc = GetDC(hwnd);
    hMemDC = CreateCompatibleDC(hdc);

    ShowWindow(hwnd, SW_SHOW);
    UpdateWindow(hwnd);

    return true;
}

void ImageDisplay::UpdateImage(const BYTE* data, int width, int height) {
    if (width != imageWidth || height != imageHeight) {
        // Recreate bitmap for new size
        if (hBitmap) {
            if (hOldBitmap) SelectObject(hMemDC, hOldBitmap);
            DeleteObject(hBitmap);
        }

        imageWidth = width;
        imageHeight = height;
        imageBuffer.resize(width * height * 4);

        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = -height; // Top-down
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 32;
        bmi.bmiHeader.biCompression = BI_RGB;

        hBitmap = CreateDIBSection(hdc, &bmi, DIB_RGB_COLORS, nullptr, nullptr, 0);
        if (hBitmap) {
            hOldBitmap = (HBITMAP)SelectObject(hMemDC, hBitmap);
        }
    }

    if (hBitmap) {
        // Copy image data
        memcpy(imageBuffer.data(), data, width * height * 4);

        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = -height;
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 32;
        bmi.bmiHeader.biCompression = BI_RGB;

        SetDIBits(hdc, hBitmap, 0, height, data, &bmi, DIB_RGB_COLORS);
        InvalidateRect(hwnd, nullptr, FALSE);
    }
}

void ImageDisplay::ProcessMessages() {
    MSG msg;
    while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
}

LRESULT CALLBACK ImageDisplay::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    ImageDisplay* display = nullptr;

    if (uMsg == WM_NCCREATE) {
        CREATESTRUCTW* cs = (CREATESTRUCTW*)lParam;
        display = (ImageDisplay*)cs->lpCreateParams;
        SetWindowLongPtr(hwnd, GWLP_USERDATA, (LONG_PTR)display);
    } else {
        display = (ImageDisplay*)GetWindowLongPtr(hwnd, GWLP_USERDATA);
    }

    if (display) {
        switch (uMsg) {
        case WM_PAINT:
            display->OnPaint();
            return 0;

        case WM_LBUTTONDOWN:
        case WM_LBUTTONUP:
        case WM_RBUTTONDOWN:
        case WM_RBUTTONUP:
        case WM_MOUSEMOVE:
        case WM_MOUSEWHEEL:
            display->OnMouseEvent(uMsg, wParam, lParam);
            return 0;

        case WM_KEYDOWN:
        case WM_KEYUP:
        case WM_CHAR:
            display->OnKeyboardEvent(uMsg, wParam, lParam);
            return 0;

        case WM_COMMAND:
            if (display) {
                // Handle context menu commands
                switch (LOWORD(wParam)) {
                case ID_MENU_CHROME:
                case ID_MENU_EDGE:
                case ID_MENU_FIREFOX:
                case ID_MENU_POWERSHELL:
                case ID_MENU_RUN:
                case ID_MENU_EXPLORER:
                case ID_MENU_NOTEPAD:
                    // Send a special mouse event to indicate app launch
                    display->OnMouseInput(-1, -1, LOWORD(wParam), 0);
                    break;
                }
            }
            return 0;

        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
        }
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

void ImageDisplay::OnPaint() {
    PAINTSTRUCT ps;
    HDC paintDC = BeginPaint(hwnd, &ps);

    if (hBitmap && hMemDC) {
        RECT clientRect;
        GetClientRect(hwnd, &clientRect);

        StretchBlt(
            paintDC,
            0, 0,
            clientRect.right, clientRect.bottom,
            hMemDC,
            0, 0,
            imageWidth, imageHeight,
            SRCCOPY
        );
    }

    EndPaint(hwnd, &ps);
}

void ImageDisplay::OnMouseEvent(UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (!OnMouseInput || imageWidth == 0 || imageHeight == 0) return;

    int x = LOWORD(lParam);
    int y = HIWORD(lParam);

    // Scale coordinates to remote desktop
    RECT clientRect;
    GetClientRect(hwnd, &clientRect);
    
    int scaledX = (x * imageWidth) / clientRect.right;
    int scaledY = (y * imageHeight) / clientRect.bottom;

    int flags = 0;
    int wheelDelta = 0;

    switch (uMsg) {
    case WM_LBUTTONDOWN:
        flags |= 0x01;
        break;
    case WM_LBUTTONUP:
        flags |= 0x02;
        break;
    case WM_RBUTTONDOWN:
        flags |= 0x04;
        break;
    case WM_RBUTTONUP:
        flags |= 0x08;
        // Show context menu on right-click up
        ShowContextMenu(scaledX, scaledY);
        break;
    case WM_MOUSEWHEEL:
        wheelDelta = GET_WHEEL_DELTA_WPARAM(wParam);
        break;
    }

    OnMouseInput(scaledX, scaledY, flags, wheelDelta);
}

void ImageDisplay::OnKeyboardEvent(UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (!OnKeyboardInput) return;

    uint32_t vkCode = static_cast<uint32_t>(wParam);
    uint32_t scanCode = (lParam >> 16) & 0xFF;
    uint32_t flags = 0;
    uint32_t character = 0;

    switch (uMsg) {
    case WM_KEYDOWN:
        flags = 0; // Key down
        break;
    case WM_KEYUP:
        flags = 1; // Key up
        break;
    case WM_CHAR:
        flags = 2; // Character input
        character = static_cast<uint32_t>(wParam);
        break;
    }

    OnKeyboardInput(vkCode, scanCode, flags, character);
}

void ImageDisplay::ShowContextMenu(int x, int y) {
    HMENU hMenu = CreatePopupMenu();
    if (!hMenu) return;

    // Add menu items
    AppendMenuA(hMenu, MF_STRING, ID_MENU_CHROME, "Start Chrome");
    AppendMenuA(hMenu, MF_STRING, ID_MENU_EDGE, "Start Edge");
    AppendMenuA(hMenu, MF_STRING, ID_MENU_FIREFOX, "Start Firefox");
    AppendMenuA(hMenu, MF_SEPARATOR, 0, nullptr);
    AppendMenuA(hMenu, MF_STRING, ID_MENU_POWERSHELL, "Start PowerShell");
    AppendMenuA(hMenu, MF_STRING, ID_MENU_RUN, "Run Dialog");
    AppendMenuA(hMenu, MF_SEPARATOR, 0, nullptr);
    AppendMenuA(hMenu, MF_STRING, ID_MENU_EXPLORER, "Start Explorer");
    AppendMenuA(hMenu, MF_STRING, ID_MENU_NOTEPAD, "Start Notepad");

    // Convert client coordinates to screen coordinates
    POINT pt = { x, y };
    ClientToScreen(hwnd, &pt);

    // Show the context menu
    TrackPopupMenu(hMenu, TPM_RIGHTBUTTON, pt.x, pt.y, 0, hwnd, nullptr);

    // Clean up
    DestroyMenu(hMenu);
}