
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <thread>
#include <atomic>
#include "../Common/Protocol.h"
#include "ImageDisplay.h"

#pragma comment(lib, "ws2_32.lib")

class RemoteDesktopServer {
private:
    SOCKET serverSocket;
    SOCKET clientSocket;
    ImageDisplay display;
    std::atomic<bool> running;
    std::thread networkThread;

public:
    RemoteDesktopServer() : serverSocket(INVALID_SOCKET), clientSocket(INVALID_SOCKET), running(false) {}
    
    ~RemoteDesktopServer() {
        Cleanup();
    }

    bool Initialize() {
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            std::cerr << "WSAStartup failed" << std::endl;
            return false;
        }

        if (!display.CreateDisplayWindow()) {
            return false;
        }

        // Set mouse input callback
        display.OnMouseInput = [this](int x, int y, int flags, int wheelDelta) {
            SendMouseInput(x, y, flags, wheelDelta);
        };

        return true;
    }

    bool StartServer() {
        serverSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (serverSocket == INVALID_SOCKET) {
            std::cerr << "Socket creation failed" << std::endl;
            return false;
        }

        sockaddr_in serverAddr = {};
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_addr.s_addr = INADDR_ANY;
        serverAddr.sin_port = htons(DEFAULT_PORT);

        if (bind(serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            std::cerr << "Bind failed. Error: " << WSAGetLastError() << std::endl;
            return false;
        }

        if (listen(serverSocket, 1) == SOCKET_ERROR) {
            std::cerr << "Listen failed" << std::endl;
            return false;
        }

        std::cout << "Server listening on port " << DEFAULT_PORT << std::endl;
        return true;
    }

    bool WaitForClient() {
        sockaddr_in clientAddr;
        int clientAddrSize = sizeof(clientAddr);
        
        clientSocket = accept(serverSocket, (sockaddr*)&clientAddr, &clientAddrSize);
        if (clientSocket == INVALID_SOCKET) {
            std::cerr << "Accept failed" << std::endl;
            return false;
        }

        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
        std::cout << "Client connected from: " << clientIP << std::endl;

        running = true;
        networkThread = std::thread(&RemoteDesktopServer::NetworkLoop, this);
        
        return true;
    }

    void Run() {
        while (running) {
            display.ProcessMessages();
            Sleep(16); // ~60 FPS for UI updates
        }
    }

    void Stop() {
        running = false;
        if (networkThread.joinable()) {
            networkThread.join();
        }
    }

private:
    void NetworkLoop() {
        std::vector<BYTE> buffer(BUFFER_SIZE);
        
        while (running) {
            PacketHeader header;
            int received = recv(clientSocket, (char*)&header, sizeof(header), 0);
            
            if (received != sizeof(header)) {
                if (received <= 0) {
                    std::cout << "Client disconnected" << std::endl;
                }
                break;
            }

            if (header.type == PACKET_IMAGE) {
                ReceiveImage(header);
            }
        }
        
        running = false;
    }

    void ReceiveImage(const PacketHeader& header) {
        // Receive image packet info
        ImagePacket imgPacket;
        memcpy(&imgPacket.header, &header, sizeof(header));
        
        int received = recv(clientSocket, ((char*)&imgPacket) + sizeof(header), 
                           sizeof(imgPacket) - sizeof(header), 0);
        
        if (received != sizeof(imgPacket) - sizeof(header)) {
            return;
        }

        // Calculate image data size
        size_t imageDataSize = header.size - sizeof(imgPacket);
        std::vector<BYTE> imageData(imageDataSize);
        
        // Receive image data
        size_t totalReceived = 0;
        while (totalReceived < imageDataSize && running) {
            size_t chunkSize = min(BUFFER_SIZE, imageDataSize - totalReceived);
            int received = recv(clientSocket, (char*)imageData.data() + totalReceived, chunkSize, 0);
            
            if (received <= 0) {
                std::cerr << "Failed to receive image data" << std::endl;
                return;
            }
            
            totalReceived += received;
        }

        // Update display
        display.UpdateImage(imageData.data(), imgPacket.width, imgPacket.height);
    }

    void SendMouseInput(int x, int y, int flags, int wheelDelta) {
        if (clientSocket == INVALID_SOCKET) return;

        MouseInputPacket packet = {};
        packet.header.type = PACKET_MOUSE_INPUT;
        packet.header.size = sizeof(packet);
        packet.x = x;
        packet.y = y;
        packet.flags = flags;
        packet.wheelDelta = wheelDelta;

        send(clientSocket, (char*)&packet, sizeof(packet), 0);
    }

    void Cleanup() {
        running = false;
        
        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        
        if (serverSocket != INVALID_SOCKET) {
            closesocket(serverSocket);
            serverSocket = INVALID_SOCKET;
        }
        
        WSACleanup();
    }
};

int main() {
    std::cout << "Remote Desktop Server" << std::endl;
    
    RemoteDesktopServer server;
    
    if (!server.Initialize()) {
        std::cerr << "Failed to initialize server" << std::endl;
        return 1;
    }
    
    if (!server.StartServer()) {
        return 1;
    }
    
    std::cout << "Waiting for client connection..." << std::endl;
    if (!server.WaitForClient()) {
        return 1;
    }
    
    std::cout << "Client connected. Starting remote desktop session..." << std::endl;
    server.Run();
    
    return 0;
}