#include "HiddenDesktop.h"
#include <iostream>
#include <sstream>
#include <random>

HiddenDesktop::HiddenDesktop() 
    : hOriginalDesktop(nullptr), hHiddenDesktop(nullptr), 
      hWindowStation(nullptr), isCreated(false) {
    memset(&explorerProcess, 0, sizeof(explorerProcess));
}

HiddenDesktop::~HiddenDesktop() {
    Cleanup();
}

bool HiddenDesktop::CreateHiddenDesktop() {
    // Get current desktop and window station
    hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
    hWindowStation = GetProcessWindowStation();

    if (!hOriginalDesktop || !hWindowStation) {
        std::cerr << "Failed to get current desktop/window station" << std::endl;
        return false;
    }

    // Generate unique desktop name
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1000, 9999);

    std::wstringstream ss;
    ss << L"HiddenDesktop_" << dis(gen);
    desktopName = ss.str();

    // Convert wide string to ANSI for CreateDesktopA
    std::string desktopNameA;
    int len = WideCharToMultiByte(CP_ACP, 0, desktopName.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (len > 0) {
        desktopNameA.resize(len - 1);
        WideCharToMultiByte(CP_ACP, 0, desktopName.c_str(), -1, &desktopNameA[0], len, nullptr, nullptr);
    }

    // Create hidden desktop using CreateDesktopA as requested
    // Include DESKTOP_SWITCHDESKTOP for proper desktop switching
    hHiddenDesktop = CreateDesktopA(
        desktopNameA.c_str(),
        nullptr,
        nullptr,
        0,
        DESKTOP_CREATEMENU | DESKTOP_CREATEWINDOW | DESKTOP_ENUMERATE |
        DESKTOP_HOOKCONTROL | DESKTOP_JOURNALPLAYBACK | DESKTOP_JOURNALRECORD |
        DESKTOP_READOBJECTS | DESKTOP_SWITCHDESKTOP | DESKTOP_WRITEOBJECTS,
        nullptr
    );

    if (!hHiddenDesktop) {
        DWORD error = GetLastError();
        std::cerr << "Failed to create hidden desktop using CreateDesktopA. Error: " << error << std::endl;

        // Fallback to CreateDesktopW if CreateDesktopA fails
        std::cout << "Attempting fallback to CreateDesktopW..." << std::endl;
        hHiddenDesktop = CreateDesktopW(
            desktopName.c_str(),
            nullptr,
            nullptr,
            0,
            DESKTOP_CREATEMENU | DESKTOP_CREATEWINDOW | DESKTOP_ENUMERATE |
            DESKTOP_HOOKCONTROL | DESKTOP_JOURNALPLAYBACK | DESKTOP_JOURNALRECORD |
            DESKTOP_READOBJECTS | DESKTOP_SWITCHDESKTOP | DESKTOP_WRITEOBJECTS,
            nullptr
        );

        if (!hHiddenDesktop) {
            std::cerr << "Both CreateDesktopA and CreateDesktopW failed. Error: " << GetLastError() << std::endl;
            return false;
        }
        std::cout << "Successfully created desktop using CreateDesktopW fallback" << std::endl;
    } else {
        std::cout << "Successfully created desktop using CreateDesktopA" << std::endl;
    }

    isCreated = true;
    std::wcout << L"Created hidden desktop: " << desktopName << std::endl;
    return true;
}

bool HiddenDesktop::SwitchToHiddenDesktop() {
    if (!hHiddenDesktop) {
        std::cerr << "Hidden desktop handle is null" << std::endl;
        return false;
    }

    BOOL result = SetThreadDesktop(hHiddenDesktop);
    if (!result) {
        DWORD error = GetLastError();
        std::cerr << "SetThreadDesktop to hidden desktop failed. Error: " << error << std::endl;
    }

    return result != 0;
}

bool HiddenDesktop::SwitchToOriginalDesktop() {
    if (!hOriginalDesktop) {
        std::cerr << "Original desktop handle is null" << std::endl;
        return false;
    }

    BOOL result = SetThreadDesktop(hOriginalDesktop);
    if (!result) {
        DWORD error = GetLastError();
        std::cerr << "SetThreadDesktop to original desktop failed. Error: " << error << std::endl;
    }

    return result != 0;
}

bool HiddenDesktop::SwitchDesktopForCapture() {
    if (!hHiddenDesktop) {
        std::cerr << "Hidden desktop handle is null" << std::endl;
        return false;
    }

    // WARNING: SwitchDesktop will make the hidden desktop visible to the user
    // This is necessary for screen capture to work properly
    std::cout << "Switching to hidden desktop (this will be visible briefly)..." << std::endl;

    // Use SwitchDesktop to make the hidden desktop active
    BOOL result = SwitchDesktop(hHiddenDesktop);
    if (!result) {
        DWORD error = GetLastError();
        std::cerr << "SwitchDesktop to hidden desktop failed. Error: " << error << std::endl;
        return false;
    }

    std::cout << "Successfully switched to hidden desktop for capture" << std::endl;
    return true;
}

bool HiddenDesktop::SwitchBackFromCapture() {
    if (!hOriginalDesktop) {
        std::cerr << "Original desktop handle is null" << std::endl;
        return false;
    }

    // Use SwitchDesktop to switch back to original desktop
    BOOL result = SwitchDesktop(hOriginalDesktop);
    if (!result) {
        DWORD error = GetLastError();
        std::cerr << "SwitchDesktop back to original desktop failed. Error: " << error << std::endl;
        return false;
    }

    std::cout << "Successfully switched back to original desktop" << std::endl;
    return true;
}

HWND HiddenDesktop::GetDesktopWindow() const {
    if (!hHiddenDesktop) {
        return nullptr;
    }

    // When we switch to the hidden desktop, GetDesktopWindow() will return
    // the desktop window handle for the current (hidden) desktop
    HDESK currentDesktop = GetThreadDesktop(GetCurrentThreadId());

    if (SetThreadDesktop(hHiddenDesktop)) {
        HWND desktopWnd = ::GetDesktopWindow();
        SetThreadDesktop(currentDesktop); // Switch back
        return desktopWnd;
    }

    return nullptr;
}

bool HiddenDesktop::StartExplorer() {
    if (!hHiddenDesktop) return false;

    // Switch to hidden desktop first
    if (!SwitchToHiddenDesktop()) {
        std::cerr << "Failed to switch to hidden desktop" << std::endl;
        return false;
    }

    // Prepare startup info for explorer
    STARTUPINFOW si = { 0 };
    si.cb = sizeof(si);
    si.lpDesktop = const_cast<LPWSTR>(desktopName.c_str());
    si.dwFlags = STARTF_USEPOSITION | STARTF_USESIZE;
    si.dwX = 0;
    si.dwY = 0;
    si.dwXSize = GetSystemMetrics(SM_CXSCREEN);
    si.dwYSize = GetSystemMetrics(SM_CYSCREEN);

    // Start explorer on the hidden desktop
    wchar_t explorerPath[] = L"C:\\Windows\\explorer.exe";
    BOOL result = CreateProcessW(
        nullptr,
        explorerPath,
        nullptr,
        nullptr,
        FALSE,
        CREATE_NEW_CONSOLE,
        nullptr,
        nullptr,
        &si,
        &explorerProcess
    );

    // Switch back to original desktop
    SwitchToOriginalDesktop();

    if (!result) {
        std::cerr << "Failed to start explorer. Error: " << GetLastError() << std::endl;
        return false;
    }

    std::cout << "Explorer started on hidden desktop" << std::endl;
    return true;
}

bool HiddenDesktop::CreateTestWindow() {
    if (!hHiddenDesktop) return false;

    // Switch to hidden desktop
    HDESK currentDesktop = GetThreadDesktop(GetCurrentThreadId());
    if (!SetThreadDesktop(hHiddenDesktop)) {
        std::cerr << "Failed to switch to hidden desktop for test window creation" << std::endl;
        return false;
    }

    // Register a simple window class
    WNDCLASSA wc = {};
    wc.lpfnWndProc = DefWindowProcA;
    wc.hInstance = GetModuleHandle(nullptr);
    wc.lpszClassName = "HiddenDesktopTestWindow";
    wc.hbrBackground = (HBRUSH)(COLOR_DESKTOP + 1);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);

    RegisterClassA(&wc);

    // Create multiple test windows on the hidden desktop for better visibility
    for (int i = 0; i < 3; i++) {
        HWND testWindow = CreateWindowA(
            "HiddenDesktopTestWindow",
            ("Test Window " + std::to_string(i + 1) + " for Hidden Desktop").c_str(),
            WS_OVERLAPPEDWINDOW | WS_VISIBLE,
            100 + (i * 50), 100 + (i * 50), 400, 300,
            nullptr, nullptr,
            GetModuleHandle(nullptr),
            nullptr
        );

        if (testWindow) {
            ShowWindow(testWindow, SW_SHOW);
            UpdateWindow(testWindow);

            // Paint something visible in the window
            HDC hdc = GetDC(testWindow);
            if (hdc) {
                RECT rect;
                GetClientRect(testWindow, &rect);

                // Fill with a bright color
                HBRUSH brush = CreateSolidBrush(RGB(255, 100 + i * 50, 100 + i * 50));
                FillRect(hdc, &rect, brush);
                DeleteObject(brush);

                // Draw some text
                SetBkMode(hdc, TRANSPARENT);
                SetTextColor(hdc, RGB(255, 255, 255));
                std::string text = "Hidden Desktop Test Window " + std::to_string(i + 1);
                TextOutA(hdc, 10, 10, text.c_str(), static_cast<int>(text.length()));

                ReleaseDC(testWindow, hdc);
            }

            std::cout << "Created and painted test window " << (i + 1) << " on hidden desktop" << std::endl;
        } else {
            std::cerr << "Failed to create test window " << (i + 1) << ". Error: " << GetLastError() << std::endl;
        }
    }

    // Switch back to original desktop
    SetThreadDesktop(currentDesktop);

    return true; // Return true if we created at least one window
}

void HiddenDesktop::Cleanup() {
    // Terminate explorer if running
    if (explorerProcess.hProcess) {
        TerminateProcess(explorerProcess.hProcess, 0);
        CloseHandle(explorerProcess.hProcess);
        CloseHandle(explorerProcess.hThread);
        memset(&explorerProcess, 0, sizeof(explorerProcess));
    }

    // Switch back to original desktop
    if (hOriginalDesktop) {
        SetThreadDesktop(hOriginalDesktop);
    }

    // Close hidden desktop
    if (hHiddenDesktop) {
        CloseDesktop(hHiddenDesktop);
        hHiddenDesktop = nullptr;
    }

    isCreated = false;
}