#pragma once
#include <windows.h>
#include <cstdint>

#pragma pack(push, 1)
struct PacketHeader {
    uint32_t type;
    uint32_t size;
};

struct MouseInputPacket {
    PacketHeader header;
    int32_t x;
    int32_t y;
    uint32_t flags; // Mouse button flags
    uint32_t wheelDelta;
};

struct ImagePacket {
    PacketHeader header;
    uint32_t width;
    uint32_t height;
    uint32_t bitsPerPixel;
    // Image data follows
};
#pragma pack(pop)

enum PacketType {
    PACKET_IMAGE = 1,
    PACKET_MOUSE_INPUT = 2
};

const int DEFAULT_PORT = 8888;
const int BUFFER_SIZE = 1024 * 1024; // 1MB buffer