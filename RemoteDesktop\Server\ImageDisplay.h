#pragma once
#include <winsock2.h>
#include <windows.h>
#include <vector>
#include <functional>

class ImageDisplay {
private:
    HWND hwnd;
    HDC hdc;
    HBITMAP hBitmap;
    HBITMAP hOldBitmap;
    HDC hMemDC;
    int imageWidth;
    int imageHeight;
    std::vector<BYTE> imageBuffer;
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    void OnPaint();
    void OnMouseEvent(UINT uMsg, WPARAM wParam, LPARAM lParam);
    void ShowContextMenu(int x, int y);  // Show right-click context menu

public:
    ImageDisplay();
    ~ImageDisplay();
    
    bool CreateDisplayWindow();
    void UpdateImage(const BYTE* data, int width, int height);
    void ProcessMessages();
    HWND GetWindowHandle() const { return hwnd; }
    
    // Callback for mouse events
    std::function<void(int, int, int, int)> OnMouseInput;
};