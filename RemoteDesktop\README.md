# Remote Desktop Application

A high-performance remote desktop application built with C++ and Windows API, featuring hidden desktop capture and real-time streaming.

## Features

- **Hidden Desktop Capture**: Creates a separate desktop environment for secure screen capture
- **Real-time Streaming**: ~30 FPS screen capture and transmission
- **Mouse Input Support**: Full mouse control including clicks, movement, and wheel scrolling
- **Network Communication**: TCP-based client-server architecture
- **Cross-Desktop Compatibility**: Uses both CreateDesktopA and CreateDesktopW APIs
- **Error Handling**: Robust error handling and connection management

## Architecture

### Client Components
- **DesktopCapture**: Handles screen capture using Windows GDI
- **HiddenDesktop**: Manages hidden desktop creation and switching
- **RemoteDesktopClient**: Main client logic and network communication

### Server Components
- **ImageDisplay**: Windows GUI for displaying remote desktop
- **RemoteDesktopServer**: Server logic and network handling

### Common Components
- **Protocol**: Shared data structures and constants

## Building the Project

### Prerequisites
- Visual Studio 2019 or later
- CMake 3.15 or later
- Windows 10 or later

### Build Steps
1. Open a command prompt in the project directory
2. Run the build script:
   ```batch
   test_project.bat
   ```
   Or manually:
   ```batch
   cmake --build build --config Release
   ```

## Usage

### Running the Server
1. Open a command prompt as Administrator
2. Navigate to the project directory
3. Run: `build\bin\Release\server.exe`
4. The server will start listening on port 8888

### Running the Client
1. Open another command prompt as Administrator
2. Navigate to the project directory
3. Run: `build\bin\Release\client.exe`
4. Enter the server IP address (or press Enter for localhost)
5. The client will connect and start streaming

## Technical Details

### Desktop Architecture Understanding
**CreateDesktopA with Working Capture**:
- Creates logical desktop for window management and security
- Traditional BitBlt fails on the desktop surface itself
- **Solution**: Use `PrintWindow` to capture individual windows
- **Window Enumeration**: `EnumDesktopWindows` finds all windows on the desktop
- **PrintWindow API**: Designed specifically for capturing non-visible windows

**Working Capture Method**:
- Enumerate all windows on the hidden desktop
- Use `PrintWindow` to capture each window individually
- Composite all window captures into a complete desktop image
- This works because windows exist even on hidden desktops

### Current Implementation
**Screen Capture**:
- Uses main desktop for reliable BitBlt operations
- 32-bit color depth (RGBA)
- Top-down DIB format for compatibility
- Efficient GDI resource management

**Hidden Desktop Usage**:
- Creates isolated desktop environment with CreateDesktopA
- Starts Windows Explorer on hidden desktop
- Used for secure input processing and isolation
- Demonstrates proper API usage even with capture limitations

**Network Protocol**:
- Custom binary protocol over TCP
- Image packets with header information
- Mouse input packets for remote control
- 1MB buffer size for efficient data transfer

**Performance Optimizations**:
- Chunked data transmission
- Memory-mapped bitmap operations
- Efficient resource cleanup

## Security Considerations

- Requires administrator privileges
- Creates isolated desktop environment
- Network communication is unencrypted (consider adding TLS for production)

## Troubleshooting

### Common Issues
1. **Access Denied**: Run as Administrator
2. **Connection Failed**: Check firewall settings and port 8888
3. **Black Screen**: Wait for Explorer to fully start on hidden desktop
4. **Build Errors**: Ensure all dependencies are installed
5. **BitBlt Error 6**: Invalid handle error - the application will automatically fallback to main desktop capture

### BitBlt Error 6 (ERROR_INVALID_HANDLE) - WORKING SOLUTION!

**Root Cause**: Traditional `BitBlt` fails on hidden desktops because there's no direct display surface to copy from.

**Working Solution**: Using **window enumeration** with `PrintWindow` API - the technique that actually works for hidden desktop capture!

**Implementation Based on Working Code**:
1. **Window Enumeration**: Uses `EnumDesktopWindows` to find all windows on the hidden desktop
2. **PrintWindow Capture**: Uses `PrintWindow` API to capture each window's content individually
3. **Composite Assembly**: Combines all window captures into a complete desktop image
4. **Hidden Desktop Support**: Actually works with `CreateDesktopA` hidden desktops!

**Three-Tier Capture System**:
1. **Primary**: Window enumeration with PrintWindow (works with hidden desktops!)
2. **Secondary**: SwitchDesktop with traditional BitBlt
3. **Tertiary**: Main desktop fallback

**Benefits**:
- ✅ **True Hidden Desktop Capture**: Actually captures from CreateDesktopA desktops
- ✅ **PrintWindow Technology**: Uses the API designed for non-visible window capture
- ✅ **Complete Desktop Assembly**: Reconstructs full desktop from individual windows
- ✅ **No More BitBlt Errors**: Uses the correct API for the job

### Desktop Switching Behavior
The application now uses a sophisticated multi-method approach:

**SwitchDesktop Method (Primary)**:
- Makes the hidden desktop briefly visible during capture
- Provides the most reliable screen capture
- You may see the desktop switch momentarily

**SetThreadDesktop Method (Secondary)**:
- Less intrusive, doesn't make desktop visible
- May not work on all systems due to security restrictions
- Automatically tried if SwitchDesktop fails

**Main Desktop Fallback (Tertiary)**:
- Captures the main desktop if hidden desktop methods fail
- Always works but less secure than hidden desktop

### Debug Mode
Build in Debug mode for additional logging:
```batch
cmake --build build --config Debug
```

### Performance Tips
- Close unnecessary applications to improve capture performance
- Use wired network connection for better streaming quality
- Adjust the Sleep(33) value in CaptureLoop for different frame rates

## API References

- [CreateDesktopA](https://learn.microsoft.com/en-us/windows/win32/api/winuser/nf-winuser-createdesktopa)
- [BitBlt](https://learn.microsoft.com/en-us/windows/win32/api/wingdi/nf-wingdi-bitblt)
- [GetDIBits](https://learn.microsoft.com/en-us/windows/win32/api/wingdi/nf-wingdi-getdibits)

## License

This project is for educational and research purposes.
