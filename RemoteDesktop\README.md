# Remote Desktop Application

A high-performance remote desktop application built with C++ and Windows API, featuring hidden desktop capture and real-time streaming.

## Features

- **Hidden Desktop Capture**: Creates a separate desktop environment for secure screen capture
- **Real-time Streaming**: ~30 FPS screen capture and transmission
- **Mouse Input Support**: Full mouse control including clicks, movement, and wheel scrolling
- **Network Communication**: TCP-based client-server architecture
- **Cross-Desktop Compatibility**: Uses both CreateDesktopA and CreateDesktopW APIs
- **Error Handling**: Robust error handling and connection management

## Architecture

### Client Components
- **DesktopCapture**: Handles screen capture using Windows GDI
- **HiddenDesktop**: Manages hidden desktop creation and switching
- **RemoteDesktopClient**: Main client logic and network communication

### Server Components
- **ImageDisplay**: Windows GUI for displaying remote desktop
- **RemoteDesktopServer**: Server logic and network handling

### Common Components
- **Protocol**: Shared data structures and constants

## Building the Project

### Prerequisites
- Visual Studio 2019 or later
- CMake 3.15 or later
- Windows 10 or later

### Build Steps
1. Open a command prompt in the project directory
2. Run the build script:
   ```batch
   test_project.bat
   ```
   Or manually:
   ```batch
   cmake --build build --config Release
   ```

## Usage

### Running the Server
1. Open a command prompt as Administrator
2. Navigate to the project directory
3. Run: `build\bin\Release\server.exe`
4. The server will start listening on port 8888

### Running the Client
1. Open another command prompt as Administrator
2. Navigate to the project directory
3. Run: `build\bin\Release\client.exe`
4. Enter the server IP address (or press Enter for localhost)
5. The client will connect and start streaming

## Technical Details

### Desktop Capture
- Uses BitBlt for efficient screen copying
- 32-bit color depth (RGBA)
- Top-down DIB format for compatibility

### Hidden Desktop
- Creates isolated desktop environment
- Starts Windows Explorer on hidden desktop
- Switches between desktops for capture and input

### Network Protocol
- Custom binary protocol over TCP
- Image packets with header information
- Mouse input packets for remote control
- 1MB buffer size for efficient data transfer

### Performance Optimizations
- Chunked data transmission
- Memory-mapped bitmap operations
- Efficient GDI resource management

## Security Considerations

- Requires administrator privileges
- Creates isolated desktop environment
- Network communication is unencrypted (consider adding TLS for production)

## Troubleshooting

### Common Issues
1. **Access Denied**: Run as Administrator
2. **Connection Failed**: Check firewall settings and port 8888
3. **Black Screen**: Wait for Explorer to fully start on hidden desktop
4. **Build Errors**: Ensure all dependencies are installed
5. **BitBlt Error 6**: Invalid handle error - the application will automatically fallback to main desktop capture

### BitBlt Error 6 (ERROR_INVALID_HANDLE) - FIXED!
This error occurred when trying to capture from a desktop that wasn't properly accessible. The application now includes multiple capture methods with automatic fallback:

1. **SwitchDesktop Method**: Uses SwitchDesktop API to make hidden desktop active (briefly visible)
2. **SetThreadDesktop Method**: Uses SetThreadDesktop for less intrusive switching
3. **Main Desktop Fallback**: If hidden desktop capture fails, switch to main desktop capture
4. **Benefit**: You get working remote desktop functionality with the best available method

### Desktop Switching Behavior
The application now uses a sophisticated multi-method approach:

**SwitchDesktop Method (Primary)**:
- Makes the hidden desktop briefly visible during capture
- Provides the most reliable screen capture
- You may see the desktop switch momentarily

**SetThreadDesktop Method (Secondary)**:
- Less intrusive, doesn't make desktop visible
- May not work on all systems due to security restrictions
- Automatically tried if SwitchDesktop fails

**Main Desktop Fallback (Tertiary)**:
- Captures the main desktop if hidden desktop methods fail
- Always works but less secure than hidden desktop

### Debug Mode
Build in Debug mode for additional logging:
```batch
cmake --build build --config Debug
```

### Performance Tips
- Close unnecessary applications to improve capture performance
- Use wired network connection for better streaming quality
- Adjust the Sleep(33) value in CaptureLoop for different frame rates

## API References

- [CreateDesktopA](https://learn.microsoft.com/en-us/windows/win32/api/winuser/nf-winuser-createdesktopa)
- [BitBlt](https://learn.microsoft.com/en-us/windows/win32/api/wingdi/nf-wingdi-bitblt)
- [GetDIBits](https://learn.microsoft.com/en-us/windows/win32/api/wingdi/nf-wingdi-getdibits)

## License

This project is for educational and research purposes.
