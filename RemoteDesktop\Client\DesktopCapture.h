#pragma once
#include <winsock2.h>
#include <windows.h>
#include <vector>

class DesktopCapture {
private:
    HDC hScreenDC;
    HDC hMemoryDC;
    HBITMAP hBitmap;
    HBITMAP hOldBitmap;
    HDESK targetDesktop;
    int screenWidth;
    int screenHeight;
    std::vector<BYTE> bitmapData;
    BITMAPINFO bmi;

public:
    DesktopCapture();
    ~DesktopCapture();
    
    bool Initialize(HDESK desktop = nullptr);
    bool CaptureScreen();
    bool CaptureScreenFromWindow(HWND desktopWindow);  // Capture using specific desktop window
    bool CaptureHiddenDesktop(HDESK desktop);          // Capture using window enumeration method
    const BYTE* GetImageData() const { return bitmapData.data(); }
    size_t GetImageSize() const { return bitmapData.size(); }
    int GetWidth() const { return screenWidth; }
    int GetHeight() const { return screenHeight; }
    void Cleanup();

private:
    // Helper methods for hidden desktop capture
    static BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam);
    bool PaintWindow(HWND hwnd, HDC hDc, HDC hDcScreen);
};