@echo off
REM Build script for RemoteDesktop project
setlocal

REM Check for CMake
where cmake >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: CMake not found. Please install CMake and ensure it is in your PATH.
    exit /b 1
)

REM Create build directory if it doesn't exist
if not exist build (
    mkdir build
)

REM Configure project
pushd build
cmake ..
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed.
    popd
    exit /b 1
)

REM Build project (Release configuration)
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed.
    popd
    exit /b 1
)

echo Build succeeded. Executables are in %CD%\bin
popd
endlocal
